// Fill out your copyright notice in the Description page of Project Settings.


#include "GridVisualComponent.h"
#include "Victor/Core/Grid/Components/GridMeshInstanceComponent.h"
#include "Victor/Core/Grid/Grid.h"
#include "Victor/Core/Grid/GridShapes/GridShapeDataStruct.h"
#include "Victor/Core/Grid/Utilities/TileDataStruct.h"
#include "Victor/Core/Grid/Subsystems/GridWorldSubsystem.h"

// Sets default values
UGridVisualComponent::UGridVisualComponent()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryComponentTick.bCanEverTick = false;

	//TODO
	//GridRootScene = CreateDefaultSubobject<USceneComponent>(TEXT("GridRootScene"));
	//SetRootComponent(GridRootScene);

	GridMeshInstanceComponent = CreateDefaultSubobject<UGridMeshInstanceComponent>(TEXT("GridMeshInstanceComponent"));
}

// Called when the game starts or when spawned
void UGridVisualComponent::BeginPlay()
{
	Super::BeginPlay();
	
	if (GridMeshInstanceComponent && GetOwner())
	{
		UPrimitiveComponent* RootPrimitive = Cast<UPrimitiveComponent>(GetOwner()->GetRootComponent());

		if (RootPrimitive)
		{
			// Get the collision response container from the root component
			FCollisionResponseContainer ComponentCollisionResponse = RootPrimitive->GetCollisionResponseToChannels();

			// Get the collision enabled state from the root component
			ECollisionEnabled::Type ComponentCollisionEnabled = RootPrimitive->GetCollisionEnabled();

			// Apply the settings to the InstancedStaticMesh
			GridMeshInstanceComponent->SetInstanceMeshCollision(ComponentCollisionEnabled, ComponentCollisionResponse);
		}
	}
}

void UGridVisualComponent::InitializeGridVisual(AGrid* Grid)
{
	CurrentGrid = Grid;

	const FGridShapeDataStruct* GridShapeData = CurrentGrid->GetGridShapeData();

	TSoftObjectPtr<UStaticMesh> SoftShapeMesh = GridShapeData->FlatMesh;
	UStaticMesh* ShapeMesh = SoftShapeMesh.LoadSynchronous();

	TSoftObjectPtr<UMaterialInstance> SoftFlatBorderMaterial = GridShapeData->FlatBorderMaterial;
	UMaterialInstance* FlatBorderMaterial = SoftFlatBorderMaterial.LoadSynchronous();

	FLinearColor BorderColor = FLinearColor::Black;

	GridMeshInstanceComponent->InitializeGridMeshInstance(ShapeMesh, FlatBorderMaterial, BorderColor, ECollisionEnabled::QueryOnly);

	if (GetOwner())
	{
		//TODO Need to test this logic to see if it works with what we are trying to do
		GetOwner()->SetActorLocation(FVector(0, 0, GroundOffset));
	}
}

void UGridVisualComponent::DestroyGridVisual()
{
	GridMeshInstanceComponent->ClearInstances();
}

void UGridVisualComponent::UpdateTileVisual(FTileDataStruct TileData)
{
	int Index = TileData.Index;
	GridMeshInstanceComponent->RemoveInstance(Index);

	UGridWorldSubsystem* GridWorldSubsystem = GetWorld()->GetSubsystem<UGridWorldSubsystem>();
	
	ETileTypeEnum TileType = TileData.TileType;
	bool IsWalkable = GridWorldSubsystem->IsTileWalkable(TileType);

	if (IsWalkable)
	{
		FTransform Transform = TileData.Transform;
		GridMeshInstanceComponent->AddInstance(Transform, Index);
	}
}

void UGridVisualComponent::SetGroundOffset(float NewGroundOffset)
{
	GroundOffset = NewGroundOffset;
}