// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Victor/Core/Grid/GridShapes/GridShapeEnum.h"
#include "Utilities/TileTypeEnum.h"
#include "Victor/Core/Grid/Utilities/TileDataStruct.h"
#include "Grid.generated.h"

struct FGridShapeDataStruct;

class UGridVisualComponent;

USTRUCT( BlueprintType )
struct FTraceGroundResult
{
	GENERATED_BODY()

	UPROPERTY()
	FVector FoundLocation;
	UPROPERTY()
	bool HasHitSomething;
	UPROPERTY()
	ETileTypeEnum TileType;
};

UCLASS()
class VICTOR_API AGrid : public AActor
{
	GENERATED_BODY()

	//Fields
private:
	//Components
	UPROPERTY(VisibleAnywhere, Category = "Components")
	USceneComponent* GridRootScene;

	UPROPERTY(VisibleAnywhere, Category = "Components")
	UGridVisualComponent* GridVisualComponent;

	//Blueprint Values
	UPROPERTY( EditAnywhere, Category = Spawn )
	FVector CenterLocation = FVector( 0.0f, 0.0f, 0.0f );

	UPROPERTY( EditAnywhere, Category = Spawn )
	FVector TileSize = FVector( 200.0f, 200.0f, 100.0f );

	UPROPERTY( EditAnywhere, Category = Spawn )
	FIntPoint TileCount = FIntPoint( 10, 10 );

	UPROPERTY( EditAnywhere, Category = Spawn )
	EGridShapeEnum Shape;

	//FDataTableRowHandle
	UPROPERTY( EditDefaultsOnly, meta = ( RowType = "GridShapeDataStruct" ), Category = Spawn )
	UDataTable* GridShapeDataTable;

	UPROPERTY( EditAnywhere, Category = Spawn )
	int8 GroundHeightOffset = 2;

	bool SpawningGrid = false;

	FVector BottomLeft = FVector( 0.0f, 0.0f, 0.0f );

	TMap<int, FTileDataStruct> TileDataMap;
	//Functions
public:
	// Sets default values for this actor's properties
	AGrid();

private:
	const FGridShapeDataStruct* SetStaticMeshValues();

	FVector2D GetAlignedTileCount();

	FVector FindGridBottomLeftLocation();

	FVector GetTileLocation( int XIndex, int YIndex );

	FRotator GetTriangleRotation( int XIndex, int YIndex );

	FTraceGroundResult TraceBoardGround( FVector Location );

	void SpawnGenericTiles( const FGridShapeDataStruct* GridShapeData, bool UseEnvironment );

	void SpawnHexTiles( const FGridShapeDataStruct* GridShapeData, bool UseEnvironment );

	void AddGridTile(FTileDataStruct TileData);

	//Util Functions
	FVector SnapVectorToVector( FVector Vector1, FVector Vector2 );

	bool IsFloatEven( float LocalFloat );

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick( float DeltaTime ) override;

	virtual void OnConstruction( const FTransform& Transform ) override;

	void SpawnGrid( bool UseEnvironment );

	bool IsSpawningGrid();

	void SetIsSpawningGridForDelay();

#pragma region Getters and Setters
	// CenterLocation
	const FGridShapeDataStruct* GetGridShapeData();

	FVector GetCenterLocation();
	void SetCenterLocation( FVector& NewCenterLocation );

	//BottomLeft
	FVector GetBottomLeft();

	// TileSize
	FVector GetTileSize();
	void SetTileSize( FVector& NewTileSize );

	// TileCount
	FIntPoint GetTileCount();
	void SetTileCount( FIntPoint& NewTileCount );

	// Shape
	EGridShapeEnum GetShape();
	void SetShape( EGridShapeEnum& NewShape );
#pragma endregion
};
