// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GridMeshInstanceComponent.generated.h"

//TODO This doesnt need to be blueprintable if we are not able to set the collision in blueprints
UCLASS()
class VICTOR_API UGridMeshInstanceComponent : public UActorComponent
{
	GENERATED_BODY()
	
	//Fields
private:
	//Components
	UPROPERTY(VisibleAnywhere, Category = "Components")
	UInstancedStaticMeshComponent* InstancedStaticMesh;

	TArray<int> InstanceIndexArray;

	//Functions
public:	
	// Sets default values for this actor's properties
	UGridMeshInstanceComponent();

protected:
	virtual void BeginPlay() override;

public:	
	void AddInstance(FTransform InstanceTransform, int Index);

	void RemoveInstance(int Index);

	void ClearInstances();

	void InitializeGridMeshInstance(UStaticMesh* <PERSON>hape<PERSON>esh, UMaterialInstance* ShapeMaterial, FLinearColor ShapeColor, ECollisionEnabled::Type Collision);

	void SetInstanceMeshCollision(ECollisionEnabled::Type NewCollisionEnabled, FCollisionResponseContainer NewResponses);
};
