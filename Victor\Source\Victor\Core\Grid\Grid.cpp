// Fill out your copyright notice in the Description page of Project Settings.


#include "Grid.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Victor/Core/Grid/GridShapes/GridShapeDataStruct.h"
#include "Math/UnrealMathUtility.h"
#include "Engine/World.h"
#include "CollisionShape.h"
#include "GridModifier.h"
#include "Subsystems/GridWorldSubsystem.h"
#include "Components/GridVisualComponent.h"
#include "Victor/Core/Grid/Utilities/TileDataStruct.h"

// Sets default values
AGrid::AGrid()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
	
	GridRootScene = CreateDefaultSubobject<USceneComponent>(TEXT("GridRootScene"));
	SetRootComponent(GridRootScene);

	GridVisualComponent = CreateDefaultSubobject<UGridVisualComponent>(TEXT("GridVisualComponent"));
}

void AGrid::OnConstruction( const FTransform& Transform )
{
	Super::OnConstruction( Transform );

	CenterLocation = GetActorLocation();
	SpawnGrid(true);
}

// Called when the game starts or when spawned
void AGrid::BeginPlay()
{
	Super::BeginPlay();

	GridVisualComponent->SetGroundOffset(GroundHeightOffset);
}

// Called every frame
void AGrid::Tick( float DeltaTime )
{
	Super::Tick( DeltaTime );

}

const FGridShapeDataStruct* AGrid::SetStaticMeshValues()
{
	// Use the stored data table pointer
	const FGridShapeDataStruct* GridShapeData = GetGridShapeData();

	if( GridShapeData )
	{
		TSoftObjectPtr<UStaticMesh> SoftShapeMesh = GridShapeData->FlatMesh;
		UStaticMesh* ShapeMesh = SoftShapeMesh.LoadSynchronous();

		TSoftObjectPtr<UMaterialInstance> SoftFlatBorderMaterial = GridShapeData->FlatBorderMaterial;
		UMaterialInstance* FlatBorderMaterial = SoftFlatBorderMaterial.LoadSynchronous();

		if( ShapeMesh && FlatBorderMaterial )
		{
			GridVisualComponent->InitializeGridVisual(this);
		}
	}
	else
	{
		UE_LOG( LogTemp, Warning, TEXT( "No Row" ) );
	}

	return GridShapeData;
}

FVector AGrid::SnapVectorToVector( FVector Vector1, FVector Vector2 )
{
	float AlignedX = FMath::GridSnap( Vector1.X, Vector2.X );
	float AlignedY = FMath::GridSnap( Vector1.Y, Vector2.Y );
	float AlignedZ = FMath::GridSnap( Vector1.Z, Vector2.Z );

	FVector AlignedVector = FVector( AlignedX, AlignedY, AlignedZ );
	return AlignedVector;
}

bool AGrid::IsFloatEven( float LocalFloat )
{
	int64 IntValue = static_cast< int64 >( FMath::RoundToFloat( LocalFloat ) );
	float ModFloat = IntValue % 2;

	bool IsEven;
	if( ModFloat == 0 )
	{
		IsEven = true;
	}
	else
	{
		IsEven = false;
	}

	return IsEven;
}

FVector2D AGrid::GetAlignedTileCount()
{
	float IsTileCountXEven = IsFloatEven( TileCount.X );
	float IsTileCountYEven = IsFloatEven( TileCount.Y );

	float XSubtractor;
	if( IsTileCountXEven )
	{
		XSubtractor = 0;
	}
	else
	{
		XSubtractor = 1;
	}

	float YSubtractor;
	if( IsTileCountYEven )
	{
		YSubtractor = 0;
	}
	else
	{
		YSubtractor = 1;
	}

	FVector2D AlignedTileCount = FVector2D( TileCount.X - XSubtractor, TileCount.Y - YSubtractor );
	return AlignedTileCount;
}

FVector AGrid::FindGridBottomLeftLocation()
{
	FVector CalculatedBottomLeft;
	FVector AlignedCenter = FVector( 0, 0, 0 );

	if( Shape == EGridShapeEnum::Square )
	{
		AlignedCenter = SnapVectorToVector( CenterLocation, TileSize );

		FVector2D AlignedTileCount = GetAlignedTileCount();
		FVector2D HalfGridCount = AlignedTileCount / 2;
		FVector2D HalfGrid = FVector2D( TileSize.X, TileSize.Y ) * HalfGridCount;
		CalculatedBottomLeft = AlignedCenter - FVector( HalfGrid.X, HalfGrid.Y, 0.0f );
	}
	else if( Shape == EGridShapeEnum::Hexagon )
	{
		FVector HexSizeAlignment = TileSize * FVector( 1.5, 1, 1 );
		AlignedCenter = SnapVectorToVector( CenterLocation, HexSizeAlignment );

		FVector2D HalfGridCount = TileCount / FIntPoint( 3, 2 );
		FVector2D HalfGrid = FVector2D( TileSize.X, TileSize.Y ) * HalfGridCount;
		FVector AlignedHalfGrid = SnapVectorToVector( FVector( HalfGrid.X, HalfGrid.Y, 0.0f ), HexSizeAlignment );
		CalculatedBottomLeft = AlignedCenter - AlignedHalfGrid - ( TileSize * FVector( 1.5, 0.5, 1 ) );
	}
	else if( Shape == EGridShapeEnum::Triangle )
	{
		//Triangle is thinner on the x axies and thus needs to snap more on the x axis
		//Thus instead of using the normal tile size on when to snap, we instead double the x
		FVector TriangleSizeAlignment = TileSize * FVector( 2, 1, 1 );
		AlignedCenter = SnapVectorToVector( CenterLocation, TriangleSizeAlignment );

		//The GetAlignedTileCount really only works for squares since the squares line up perfectly
		//This means for triangles we have to simply align on -1,-1 instead
		FVector2D AlignedTileCount = FVector2D( TileCount.X - 1, TileCount.Y - 1 );

		//Where before we only got half, triangles have to do half for x but 1/4 for Y
		FVector2D HalfGridCount = AlignedTileCount / FVector2D( 2, 4 );
		FVector2D HalfGrid = FVector2D( TileSize.X, TileSize.Y ) * HalfGridCount;
		FVector AlignedHalfGrid = SnapVectorToVector( FVector( HalfGrid.X, HalfGrid.Y, 0.0f ), TriangleSizeAlignment );
		CalculatedBottomLeft = AlignedCenter - AlignedHalfGrid;
	}


	CenterLocation = AlignedCenter;

	return CalculatedBottomLeft;
}

FRotator AGrid::GetTriangleRotation( int XIndex, int YIndex )
{
	bool IsXEven = IsFloatEven( XIndex );
	bool IsYEven = IsFloatEven( YIndex );

	FRotator TriangleRotation = FRotator( 0, 0, 0 );
	if( ( IsXEven && !IsYEven ) || ( !IsXEven && IsYEven ) )
	{
		TriangleRotation.Yaw = 180;
	}

	return TriangleRotation;
}

FVector AGrid::GetTileLocation( int XIndex, int YIndex )
{
	float AlignedXIndex = XIndex;
	float AlignedYIndex = YIndex;
	if( Shape == EGridShapeEnum::Triangle )
	{
		AlignedYIndex = AlignedYIndex / 2;
	}
	else if( Shape == EGridShapeEnum::Hexagon )
	{
		AlignedXIndex = AlignedXIndex * .75;
		AlignedYIndex = AlignedYIndex * .5;
	}


	FVector IndexVector = FVector( AlignedXIndex, AlignedYIndex, 0 );
	FVector SizedVector = IndexVector * TileSize;
	FVector TileLocation = BottomLeft + SizedVector;

	return TileLocation;
}

FTraceGroundResult AGrid::TraceBoardGround( FVector Location )
{
	FVector StartVector = Location + FVector( 0, 0, 1000 );
	FVector EndVector = Location - FVector( 0, 0, 1000 );

	int RadiusReduction = 3;
	if( Shape == EGridShapeEnum::Triangle )
	{
		RadiusReduction = 5;
	}

	float SphereRadius = TileSize.X / RadiusReduction;
	FCollisionShape Sphere = FCollisionShape::MakeSphere( SphereRadius );

	TArray <FHitResult> HitResultArray;
	bool HasHit = GetWorld()->SweepMultiByChannel(HitResultArray, StartVector, EndVector, FQuat::Identity, ECollisionChannel::ECC_GameTraceChannel1, Sphere );

	FTraceGroundResult Result;
	Result.HasHitSomething = HasHit;

	if( HasHit )
	{
		ETileTypeEnum TileType = ETileTypeEnum::Normal;
		for (FHitResult HitResult : HitResultArray)
		{
			AActor* HitActor = HitResult.GetActor();
			AGridModifier* GridModifier = Cast<AGridModifier>(HitActor);
			if (GridModifier)
			{
				TileType = GridModifier->GetTileType();
			}
			else
			{
				float AlignedZ = FMath::GridSnap(HitActor->GetActorLocation().Z, TileSize.Z);
				//TODO Commenting out this to make sure this works
				//Result.FoundLocation = FVector(Location.X, Location.Y, AlignedZ + GroundHeightOffset);
				Result.FoundLocation = FVector(Location.X, Location.Y, 0);
			}
		}
		Result.TileType = TileType;
	}
	else
	{
		//TODO Commenting out this to make sure this works
		//Result.FoundLocation = Location + FVector( 0, 0, GroundHeightOffset );
		Result.FoundLocation = Location + FVector(0, 0, 0);
	}

	return Result;
}

void AGrid::AddGridTile(FTileDataStruct TileData)
{
	TileDataMap.Add(TileData.Index, TileData);

	GridVisualComponent->UpdateTileVisual(TileData);
}

void AGrid::SpawnGenericTiles( const FGridShapeDataStruct* GridShapeData, bool UseEnvironment )
{
	int Index = 0;
	for( int x = 0; x < TileCount.X; x++ )
	{
		for( int y = 0; y < TileCount.Y; y++ )
		{
			FVector MeshSize = GridShapeData->MeshSize;
			FVector TileScale = TileSize / MeshSize;

			FVector TileLocation = GetTileLocation( x, y );

			FRotator Rotation = FRotator( 0, 0, 0 );
			if( Shape == EGridShapeEnum::Triangle )
			{
				Rotation = GetTriangleRotation( x, y );
			}

			FTransform TileTransform = FTransform();
			TileTransform.SetLocation( TileLocation );
			TileTransform.SetScale3D( TileScale );
			TileTransform.SetRotation( Rotation.Quaternion() );

			if( UseEnvironment )
			{
				FTraceGroundResult Result = TraceBoardGround( TileLocation );

				FTileDataStruct TileData
				{
					.Index = Index,
					.TileType = Result.TileType,
					.Transform = TileTransform
				};
				AddGridTile(TileData);
			}
			else
			{
				FTileDataStruct TileData
				{
					.Index = Index,
					.TileType = ETileTypeEnum::Normal,
					.Transform = TileTransform
				};
				AddGridTile(TileData);
			}

			Index++;
		}
	}
}

void AGrid::SpawnHexTiles( const FGridShapeDataStruct* GridShapeData, bool UseEnvironment )
{
	int Index = 0;
	int YMaxCount = TileCount.Y * 2;
	for( int X = 0; X < TileCount.X; X++ )
	{

		int StartingY;
		if( X % 2 == 0 )
		{
			StartingY = 0;
		}
		else
		{
			StartingY = 1;
		}

		for( int Y = StartingY; Y < YMaxCount; Y++ )
		{
			Y++;
			FVector MeshSize = GridShapeData->MeshSize;
			FVector TileScale = TileSize / MeshSize;

			FVector TileLocation = GetTileLocation( X, Y );

			FRotator Rotation = FRotator( 0, 0, 0 );

			FTransform TileTransform = FTransform();
			TileTransform.SetLocation( TileLocation );
			TileTransform.SetScale3D( TileScale );
			TileTransform.SetRotation( Rotation.Quaternion() );

			if( UseEnvironment )
			{
				FTraceGroundResult Result = TraceBoardGround( TileLocation );

				FTileDataStruct TileData
				{
					.Index = Index,
					.TileType = Result.TileType,
					.Transform = TileTransform
				};
				AddGridTile(TileData);
			}
			else
			{
				FTileDataStruct TileData
				{
					.Index = Index,
					.TileType = ETileTypeEnum::Normal,
					.Transform = TileTransform
				};
				AddGridTile(TileData);
			}

			Index++;
		}
	}
}

void AGrid::SpawnGrid( bool UseEnvironment )
{
	TileDataMap.Empty();
	GridVisualComponent->DestroyGridVisual();

	if( Shape != EGridShapeEnum::None )
	{
		const FGridShapeDataStruct* GridShapeData = SetStaticMeshValues();
		if( GridShapeData )
		{
			BottomLeft = FindGridBottomLeftLocation();

			if( Shape == EGridShapeEnum::Hexagon )
			{
				SpawnHexTiles( GridShapeData, UseEnvironment );
			}
			else
			{
				SpawnGenericTiles( GridShapeData, UseEnvironment );
			}
		}
	}
	SpawningGrid = false;
}

// Getters and Setters Implementation

const FGridShapeDataStruct* AGrid::GetGridShapeData()
{
	if (!GridShapeDataTable)
	{
		return nullptr;
	}

	UEnum* EnumPtr = StaticEnum<EGridShapeEnum>();
	FString ShapeName = EnumPtr->GetNameStringByValue((int64)Shape);

	FName RowName = FName(*ShapeName);

	// Use the stored data table pointer
	const FGridShapeDataStruct* GridShapeData = GridShapeDataTable->FindRow<FGridShapeDataStruct>(RowName, TEXT("SpawnGrid"));
	return GridShapeData;
}


bool AGrid::IsSpawningGrid()
{
	return SpawningGrid;
}

void AGrid::SetIsSpawningGridForDelay()
{
	SpawningGrid = true;
}

// CenterLocation
FVector AGrid::GetCenterLocation()
{
	return CenterLocation;
}

void AGrid::SetCenterLocation( FVector& NewCenterLocation )
{
	CenterLocation = NewCenterLocation;
}

FVector AGrid::GetBottomLeft()
{
	return BottomLeft;
}

// TileSize
FVector AGrid::GetTileSize()
{
	return TileSize;
}

void AGrid::SetTileSize( FVector& NewTileSize )
{
	TileSize = NewTileSize;
}

// TileCount
FIntPoint AGrid::GetTileCount()
{
	return TileCount;
}

void AGrid::SetTileCount( FIntPoint& NewTileCount )
{
	TileCount = NewTileCount;
}

// Shape
EGridShapeEnum AGrid::GetShape()
{
	return Shape;
}

void AGrid::SetShape( EGridShapeEnum& NewShape )
{
	Shape = NewShape;
}
