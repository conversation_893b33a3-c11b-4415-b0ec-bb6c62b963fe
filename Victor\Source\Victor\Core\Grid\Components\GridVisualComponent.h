// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GridVisualComponent.generated.h"

struct FTileDataStruct;

class UGridMeshInstanceComponent;
class AGrid;

//TODO We need to change this into a component
UCLASS()
class VICTOR_API UGridVisualComponent : public UActorComponent
{
	GENERATED_BODY()
	

	//Fields
private:
	UPROPERTY(VisibleAnywhere, Category = "Components")
	UGridMeshInstanceComponent* GridMeshInstanceComponent;
	
	UPROPERTY(EditAnywhere, Category = Spawn)
	float GroundOffset = 2;

	AGrid* CurrentGrid;

	//Functions
public:	
	// Sets default values for this actor's properties
	UGridVisualComponent();

private:

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:	
	void InitializeGridVisual(AGrid* Grid);

	void DestroyGridVisual();

	void UpdateTileVisual(FTileDataStruct TileData);

	void SetGroundOffset(float NewGroundOffset);
};
